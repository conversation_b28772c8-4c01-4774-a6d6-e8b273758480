import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:http/http.dart' as http;

class OAuthHelper {
  final String clientId;
  final String redirectUri = 'urn:ietf:wg:oauth:2.0:oob';
  final List<String> scopes;

  OAuthHelper({
    required this.clientId,
    required this.scopes,
  });

  /// Generate a random string for PKCE
  String _generateRandomString(int length) {
    const chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    final random = Random.secure();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)])
        .join();
  }

  /// Generate PKCE code verifier and challenge
  Map<String, String> _generatePKCE() {
    final codeVerifier = _generateRandomString(128);
    final bytes = utf8.encode(codeVerifier);
    final digest = sha256.convert(bytes);
    final codeChallenge = base64Url.encode(digest.bytes).replaceAll('=', '');

    return {
      'code_verifier': codeVerifier,
      'code_challenge': codeChallenge,
    };
  }

  /// Build the OAuth authorization URL
  String _buildAuthUrl(String codeChallenge) {
    final params = {
      'client_id': clientId,
      'redirect_uri': redirectUri,
      'response_type': 'code',
      'scope': scopes.join(' '),
      'code_challenge': codeChallenge,
      'code_challenge_method': 'S256',
      'access_type': 'offline',
      'prompt': 'consent',
    };

    final query = params.entries
        .map((e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');

    return 'https://accounts.google.com/o/oauth2/v2/auth?$query';
  }

  /// Show OAuth web view and get authorization code
  Future<String?> getAuthorizationCode(BuildContext context) async {
    final pkce = _generatePKCE();
    final authUrl = _buildAuthUrl(pkce['code_challenge']!);

    return showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (context) => _OAuthWebView(
        authUrl: authUrl,
        redirectUri: redirectUri,
        codeVerifier: pkce['code_verifier']!,
        clientId: clientId,
      ),
    );
  }
}

class _OAuthWebView extends StatefulWidget {
  final String authUrl;
  final String redirectUri;
  final String codeVerifier;
  final String clientId;

  const _OAuthWebView({
    required this.authUrl,
    required this.redirectUri,
    required this.codeVerifier,
    required this.clientId,
  });

  @override
  State<_OAuthWebView> createState() => _OAuthWebViewState();
}

class _OAuthWebViewState extends State<_OAuthWebView> {
  late final WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (url) {
            setState(() {
              _isLoading = true;
            });
            _handleNavigation(url);
          },
          onPageFinished: (url) {
            setState(() {
              _isLoading = false;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.authUrl));
  }

  void _handleNavigation(String url) {
    if (url.contains('code=')) {
      // Extract authorization code from URL
      final uri = Uri.parse(url);
      final code = uri.queryParameters['code'];

      if (code != null) {
        _exchangeCodeForToken(code);
      }
    } else if (url.contains('error=')) {
      // Handle error
      final uri = Uri.parse(url);
      final error = uri.queryParameters['error'];
      Navigator.of(context).pop(null);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('OAuth Error: $error')),
      );
    }
  }

  Future<void> _exchangeCodeForToken(String code) async {
    try {
      final response = await http.post(
        Uri.parse('https://oauth2.googleapis.com/token'),
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'client_id': widget.clientId,
          'code': code,
          'code_verifier': widget.codeVerifier,
          'grant_type': 'authorization_code',
          'redirect_uri': widget.redirectUri,
        },
      );

      if (response.statusCode == 200) {
        final tokenData = json.decode(response.body);
        Navigator.of(context).pop(tokenData['access_token']);
      } else {
        throw Exception('Token exchange failed: ${response.body}');
      }
    } catch (e) {
      Navigator.of(context).pop(null);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Token exchange error: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Google Drive Authorization'),
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(null),
          ),
        ),
        body: Stack(
          children: [
            WebViewWidget(controller: _controller),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }
}
