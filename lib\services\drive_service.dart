import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:http/http.dart' as http;
import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;

class DriveService {
  static const List<String> _scopes = [
    drive.DriveApi.driveFileScope,
  ];

  late drive.DriveApi _driveApi;
  late AutoRefreshingAuthClient _client;

  // Your service account credentials
  static const Map<String, dynamic> _credentials = **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;

  /// Initialize the Drive service with authentication
  Future<void> initialize() async {
    try {
      // Create ServiceAccountCredentials using the correct method
      final accountCredentials = ServiceAccountCredentials(
        _credentials['client_email'],
        ClientId(_credentials['client_id'], null),
        _credentials['private_key'],
      );
      
      _client = await clientViaServiceAccount(accountCredentials, _scopes);
      _driveApi = drive.DriveApi(_client);
      print('✅ Drive service initialized successfully');
    } catch (e) {
      print('❌ Error initializing Drive service: $e');
      rethrow;
    }
  }

  /// Upload an image file to Google Drive
  /// [filePath] - Path to the local image file
  /// [fileName] - Optional custom name for the file (will use original if not provided)
  /// [folderId] - Optional folder ID to upload to (uploads to root if not provided)
  /// [description] - Optional description for the file
  Future<String?> uploadImage({
    required String filePath,
    String? fileName,
    String? folderId,
    String? description,
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('File does not exist: $filePath');
      }

      // Get file info
      final fileBytes = await file.readAsBytes();
      final mimeType = lookupMimeType(filePath) ?? 'application/octet-stream';
      final actualFileName = fileName ?? path.basename(filePath);

      // Validate it's an image
      if (!mimeType.startsWith('image/')) {
        throw Exception('File is not an image: $mimeType');
      }

      print('📤 Uploading image: $actualFileName (${fileBytes.length} bytes)');

      // Create the file metadata
      final driveFile = drive.File()
        ..name = actualFileName
        ..description = description ?? 'Uploaded via VitaBrosse Pro app'
        ..parents = folderId != null ? [folderId] : null;

      // Create media
      final media = drive.Media(
        Stream.fromIterable([fileBytes]),
        fileBytes.length,
        contentType: mimeType,
      );

      // Upload the file
      final result = await _driveApi.files.create(
        driveFile,
        uploadMedia: media,
      );

      if (result.id != null) {
        print('✅ Image uploaded successfully!');
        print('📁 File ID: ${result.id}');
        print('🔗 File name: ${result.name}');

        // Make the file publicly viewable (optional)
        await makeFilePublic(result.id!);
        return result.id;
      } else {
        throw Exception('Upload failed - no file ID returned');
      }
    } catch (e) {
      print('❌ Error uploading image: $e');
      return null;
    }
  }

  /// Upload image from bytes
  Future<String?> uploadImageFromBytes({
    required Uint8List imageBytes,
    required String fileName,
    String? folderId,
    String? description,
    String? mimeType,
  }) async {
    try {
      final actualMimeType = mimeType ?? 'image/jpeg';
      print('📤 Uploading image from bytes: $fileName (${imageBytes.length} bytes)');

      // Create the file metadata
      final driveFile = drive.File()
        ..name = fileName
        ..description = description ?? 'Uploaded via VitaBrosse Pro app'
        ..parents = folderId != null ? [folderId] : null;

      // Create media
      final media = drive.Media(
        Stream.fromIterable([imageBytes]),
        imageBytes.length,
        contentType: actualMimeType,
      );

      // Upload the file
      final result = await _driveApi.files.create(
        driveFile,
        uploadMedia: media,
      );

      if (result.id != null) {
        print('✅ Image uploaded successfully!');
        print('📁 File ID: ${result.id}');

        // Make the file publicly viewable (optional)
        await makeFilePublic(result.id!);
        return result.id;
      } else {
        throw Exception('Upload failed - no file ID returned');
      }
    } catch (e) {
      print('❌ Error uploading image from bytes: $e');
      return null;
    }
  }

  /// Make a file publicly viewable
  Future<void> makeFilePublic(String fileId) async {
    try {
      final permission = drive.Permission()
        ..role = 'reader'
        ..type = 'anyone';

      await _driveApi.permissions.create(permission, fileId);
      print('🌐 File made publicly viewable');
    } catch (e) {
      print('⚠️ Warning: Could not make file public: $e');
    }
  }

  /// Get the public URL for a file
  String getPublicUrl(String fileId) {
    return 'https://drive.google.com/file/d/$fileId/view';
  }

  /// Get the direct download URL for a file
  String getDirectUrl(String fileId) {
    return 'https://drive.google.com/uc?id=$fileId';
  }

  /// Create a folder in Google Drive
  Future<String?> createFolder({
    required String folderName,
    String? parentFolderId,
    String? description,
  }) async {
    try {
      final folder = drive.File()
        ..name = folderName
        ..mimeType = 'application/vnd.google-apps.folder'
        ..description = description ?? 'Created via VitaBrosse Pro app'
        ..parents = parentFolderId != null ? [parentFolderId] : null;

      final result = await _driveApi.files.create(folder);

      if (result.id != null) {
        print('📁 Folder created: ${result.name} (${result.id})');
        return result.id;
      }
      return null;
    } catch (e) {
      print('❌ Error creating folder: $e');
      return null;
    }
  }

  /// List files in Google Drive
  Future<List<drive.File>> listFiles({
    String? folderId,
    int maxResults = 10,
    String? query,
  }) async {
    try {
      String searchQuery = query ?? '';
      if (folderId != null) {
        searchQuery += searchQuery.isEmpty 
            ? "'$folderId' in parents" 
            : " and '$folderId' in parents";
      }

      final result = await _driveApi.files.list(
        q: searchQuery.isEmpty ? null : searchQuery,
        pageSize: maxResults,
        spaces: 'drive',
        $fields: 'files(id,name,mimeType,size,createdTime,modifiedTime)',
      );

      return result.files ?? [];
    } catch (e) {
      print('❌ Error listing files: $e');
      return [];
    }
  }

  /// Delete a file from Google Drive
  Future<bool> deleteFile(String fileId) async {
    try {
      await _driveApi.files.delete(fileId);
      print('🗑️ File deleted: $fileId');
      return true;
    } catch (e) {
      print('❌ Error deleting file: $e');
      return false;
    }
  }

  /// Dispose of the client
  void dispose() {
    _client.close();
  }
}