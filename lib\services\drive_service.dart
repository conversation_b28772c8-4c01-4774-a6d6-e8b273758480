import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:http/http.dart' as http;
import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;
import 'package:url_launcher/url_launcher.dart';
import '../models/oauth_client_credentials.dart';
import 'oauth_helper.dart';

class DriveService {
  static const List<String> _scopes = [
    drive.DriveApi.driveFileScope,
    drive.DriveApi.driveScope,
  ];

  late drive.DriveApi _driveApi;
  late AutoRefreshingAuthClient _client;
  OAuthClientCredentials? _oauthConfig;
  String? _userEmail;
  bool _isInitialized = false;

  /// Initialize the Drive service with OAuth authentication
  Future<void> initialize({BuildContext? context}) async {
    if (_isInitialized) return;

    try {
      // Load OAuth client credentials
      await _loadCredentials();

      if (context != null) {
        // Use WebView-based OAuth flow for mobile
        await _initializeWithWebView(context);
      } else {
        // Fallback to console-based OAuth flow
        await _initializeWithConsole();
      }

      _driveApi = drive.DriveApi(_client);
      _isInitialized = true;

      print('✅ Drive service initialized successfully with OAuth');
    } catch (e) {
      print('❌ Error initializing Drive service: $e');
      rethrow;
    }
  }

  /// Initialize with WebView-based OAuth (mobile-friendly)
  Future<void> _initializeWithWebView(BuildContext context) async {
    final oauthHelper = OAuthHelper(
      clientId: _oauthConfig!.clientId,
      scopes: _scopes,
    );

    final accessToken = await oauthHelper.getAuthorizationCode(context);

    if (accessToken == null) {
      throw Exception('OAuth authorization was cancelled or failed');
    }

    // Create access credentials with the token
    final credentials = AccessCredentials(
      AccessToken(
          'Bearer', accessToken, DateTime.now().add(Duration(hours: 1))),
      null, // No refresh token in this flow
      _scopes,
    );

    final clientId = ClientId(_oauthConfig!.clientId, null);

    _client = autoRefreshingClient(
      clientId,
      credentials,
      http.Client(),
    );

    _userEmail = '<EMAIL>'; // Placeholder
  }

  /// Initialize with console-based OAuth (fallback)
  Future<void> _initializeWithConsole() async {
    final clientId = ClientId(
      _oauthConfig!.clientId,
      null, // No client secret for installed apps
    );

    // Perform OAuth flow with user consent
    _client = await clientViaUserConsent(
      clientId,
      _scopes,
      _promptUser,
    );

    _userEmail = '<EMAIL>'; // Placeholder
  }

  /// Load OAuth client credentials from assets
  Future<void> _loadCredentials() async {
    try {
      final credentialsJson =
          await rootBundle.loadString('assets/credentials/oauth_client.json');
      final credentialsMap =
          json.decode(credentialsJson) as Map<String, dynamic>;
      _oauthConfig = OAuthClientCredentials.fromJson(credentialsMap);
      print('✅ OAuth credentials loaded successfully');
    } catch (e) {
      print('❌ Error loading OAuth credentials: $e');
      rethrow;
    }
  }

  /// Prompt user for OAuth consent
  Future<void> _promptUser(String url) async {
    print('🔗 Opening OAuth authorization URL...');
    print('URL: $url');

    // Try to launch the URL
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      print('❌ Could not launch URL automatically');
      throw Exception(
          'Could not open authorization URL. Please ensure you have a web browser available.');
    }
  }

  /// Check if user is signed in
  bool get isSignedIn => _isInitialized;

  /// Get current user email (not available with this OAuth flow)
  String? get currentUserEmail => _userEmail;

  /// Sign out from Google (close the client)
  Future<void> signOut() async {
    if (_isInitialized) {
      _client.close();
      _isInitialized = false;
      _userEmail = null;
      print('👋 Signed out from Google Drive');
    }
  }

  /// Upload an image file to Google Drive
  /// [filePath] - Path to the local image file
  /// [fileName] - Optional custom name for the file (will use original if not provided)
  /// [folderId] - Optional folder ID to upload to (uploads to root if not provided)
  /// [description] - Optional description for the file
  Future<String?> uploadImage({
    required String filePath,
    String? fileName,
    String? folderId,
    String? description,
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('File does not exist: $filePath');
      }

      // Get file info
      final fileBytes = await file.readAsBytes();
      final mimeType = lookupMimeType(filePath) ?? 'application/octet-stream';
      final actualFileName = fileName ?? path.basename(filePath);

      // Validate it's an image
      if (!mimeType.startsWith('image/')) {
        throw Exception('File is not an image: $mimeType');
      }

      print('📤 Uploading image: $actualFileName (${fileBytes.length} bytes)');

      // Create the file metadata
      final driveFile = drive.File()
        ..name = actualFileName
        ..description = description ?? 'Uploaded via VitaBrosse Pro app'
        ..parents = folderId != null ? [folderId] : null;

      // Create media
      final media = drive.Media(
        Stream.fromIterable([fileBytes]),
        fileBytes.length,
        contentType: mimeType,
      );

      // Upload the file
      final result = await _driveApi.files.create(
        driveFile,
        uploadMedia: media,
      );

      if (result.id != null) {
        print('✅ Image uploaded successfully!');
        print('📁 File ID: ${result.id}');
        print('🔗 File name: ${result.name}');

        // Make the file publicly viewable (optional)
        await makeFilePublic(result.id!);
        return result.id;
      } else {
        throw Exception('Upload failed - no file ID returned');
      }
    } catch (e) {
      print('❌ Error uploading image: $e');
      return null;
    }
  }

  /// Upload image from bytes
  Future<String?> uploadImageFromBytes({
    required Uint8List imageBytes,
    required String fileName,
    String? folderId,
    String? description,
    String? mimeType,
  }) async {
    try {
      final actualMimeType = mimeType ?? 'image/jpeg';
      print(
          '📤 Uploading image from bytes: $fileName (${imageBytes.length} bytes)');

      // Create the file metadata
      final driveFile = drive.File()
        ..name = fileName
        ..description = description ?? 'Uploaded via VitaBrosse Pro app'
        ..parents = folderId != null ? [folderId] : null;

      // Create media
      final media = drive.Media(
        Stream.fromIterable([imageBytes]),
        imageBytes.length,
        contentType: actualMimeType,
      );

      // Upload the file
      final result = await _driveApi.files.create(
        driveFile,
        uploadMedia: media,
      );

      if (result.id != null) {
        print('✅ Image uploaded successfully!');
        print('📁 File ID: ${result.id}');

        // Make the file publicly viewable (optional)
        await makeFilePublic(result.id!);
        return result.id;
      } else {
        throw Exception('Upload failed - no file ID returned');
      }
    } catch (e) {
      print('❌ Error uploading image from bytes: $e');
      return null;
    }
  }

  /// Make a file publicly viewable
  Future<void> makeFilePublic(String fileId) async {
    try {
      final permission = drive.Permission()
        ..role = 'reader'
        ..type = 'anyone';

      await _driveApi.permissions.create(permission, fileId);
      print('🌐 File made publicly viewable');
    } catch (e) {
      print('⚠️ Warning: Could not make file public: $e');
    }
  }

  /// Get the public URL for a file
  String getPublicUrl(String fileId) {
    return 'https://drive.google.com/file/d/$fileId/view';
  }

  /// Get the direct download URL for a file
  String getDirectUrl(String fileId) {
    return 'https://drive.google.com/uc?id=$fileId';
  }

  /// Create a folder in Google Drive
  Future<String?> createFolder({
    required String folderName,
    String? parentFolderId,
    String? description,
  }) async {
    try {
      final folder = drive.File()
        ..name = folderName
        ..mimeType = 'application/vnd.google-apps.folder'
        ..description = description ?? 'Created via VitaBrosse Pro app'
        ..parents = parentFolderId != null ? [parentFolderId] : null;

      final result = await _driveApi.files.create(folder);

      if (result.id != null) {
        print('📁 Folder created: ${result.name} (${result.id})');
        return result.id;
      }
      return null;
    } catch (e) {
      print('❌ Error creating folder: $e');
      return null;
    }
  }

  /// List files in Google Drive
  Future<List<drive.File>> listFiles({
    String? folderId,
    int maxResults = 10,
    String? query,
  }) async {
    try {
      String searchQuery = query ?? '';
      if (folderId != null) {
        searchQuery += searchQuery.isEmpty
            ? "'$folderId' in parents"
            : " and '$folderId' in parents";
      }

      final result = await _driveApi.files.list(
        q: searchQuery.isEmpty ? null : searchQuery,
        pageSize: maxResults,
        spaces: 'drive',
        $fields: 'files(id,name,mimeType,size,createdTime,modifiedTime)',
      );

      return result.files ?? [];
    } catch (e) {
      print('❌ Error listing files: $e');
      return [];
    }
  }

  /// Delete a file from Google Drive
  Future<bool> deleteFile(String fileId) async {
    try {
      await _driveApi.files.delete(fileId);
      print('🗑️ File deleted: $fileId');
      return true;
    } catch (e) {
      print('❌ Error deleting file: $e');
      return false;
    }
  }

  /// Dispose of the client
  void dispose() {
    _client.close();
  }
}
