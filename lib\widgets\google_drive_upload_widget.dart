import 'package:flutter/material.dart';
import 'dart:io';
import '../services/drive_service.dart';
import '../utils/file_picker_helper.dart';
import '../models/upload_result.dart';

class GoogleDriveUploadWidget extends StatefulWidget {
  final Function(List<UploadResult>)? onUploadComplete;
  final String? folderId;
  final bool allowMultiple;
  final String title;

  const GoogleDriveUploadWidget({
    Key? key,
    this.onUploadComplete,
    this.folderId,
    this.allowMultiple = true,
    this.title = 'Télécharger vers Google Drive',
  }) : super(key: key);

  @override
  State<GoogleDriveUploadWidget> createState() => _GoogleDriveUploadWidgetState();
}

class _GoogleDriveUploadWidgetState extends State<GoogleDriveUploadWidget> {
  final DriveService _driveService = DriveService();
  bool _isInitialized = false;
  bool _isUploading = false;
  List<UploadResult> _uploadResults = [];
  String _statusMessage = 'Initialisation...';

  @override
  void initState() {
    super.initState();
    _initializeDriveService();
  }

  @override
  void dispose() {
    if (_isInitialized) {
      _driveService.dispose();
    }
    super.dispose();
  }

  Future<void> _initializeDriveService() async {
    setState(() {
      _statusMessage = 'Initialisation du service Google Drive...';
    });

    try {
      await _driveService.initialize(context: context);
      setState(() {
        _isInitialized = true;
        _statusMessage = 'Connecté à Google Drive (${_driveService.currentUserEmail})';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Erreur d\'initialisation: $e';
      });
      
      // Show error dialog with retry option
      if (mounted) {
        _showAuthErrorDialog(e.toString());
      }
    }
  }

  void _showAuthErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Authentification Google Drive'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Une erreur s\'est produite lors de la connexion à Google Drive:'),
            const SizedBox(height: 8),
            Text(
              error,
              style: TextStyle(
                color: Colors.red[700],
                fontSize: 12,
                fontFamily: 'monospace',
              ),
            ),
            const SizedBox(height: 16),
            const Text('Veuillez vous assurer que:'),
            const Text('• Vous avez une connexion internet'),
            const Text('• Vous acceptez les permissions Google Drive'),
            const Text('• Votre compte Google est actif'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _initializeDriveService();
            },
            child: const Text('Réessayer'),
          ),
        ],
      ),
    );
  }

  Future<void> _uploadImageFromGallery() async {
    if (!_isInitialized) {
      _showSnackBar('Service non initialisé');
      return;
    }

    setState(() {
      _isUploading = true;
      _statusMessage = 'Sélection d\'image depuis la galerie...';
    });

    try {
      final File? imageFile = await FilePickerHelper.pickImageFromGallery();
      if (imageFile != null) {
        await _uploadSingleFile(imageFile);
      } else {
        setState(() {
          _statusMessage = 'Aucune image sélectionnée';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Erreur: $e';
      });
      _showSnackBar('Erreur: $e');
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  Future<void> _uploadImageFromCamera() async {
    if (!_isInitialized) {
      _showSnackBar('Service non initialisé');
      return;
    }

    setState(() {
      _isUploading = true;
      _statusMessage = 'Ouverture de l\'appareil photo...';
    });

    try {
      final File? imageFile = await FilePickerHelper.pickImageFromCamera();
      if (imageFile != null) {
        await _uploadSingleFile(imageFile);
      } else {
        setState(() {
          _statusMessage = 'Aucune photo prise';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Erreur: $e';
      });
      _showSnackBar('Erreur: $e');
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  Future<void> _uploadMultipleImages() async {
    if (!_isInitialized || !widget.allowMultiple) {
      _showSnackBar('Service non initialisé ou sélection multiple non autorisée');
      return;
    }

    setState(() {
      _isUploading = true;
      _statusMessage = 'Sélection de plusieurs images...';
    });

    try {
      final List<File> imageFiles = await FilePickerHelper.pickMultipleImages();
      if (imageFiles.isNotEmpty) {
        setState(() {
          _statusMessage = 'Téléchargement de ${imageFiles.length} images...';
        });

        for (int i = 0; i < imageFiles.length; i++) {
          final file = imageFiles[i];
          setState(() {
            _statusMessage = 'Téléchargement ${i + 1}/${imageFiles.length}: ${file.path.split('/').last}';
          });

          await _uploadSingleFile(file, showIndividualStatus: false);
        }

        setState(() {
          _statusMessage = 'Téléchargement terminé: ${_uploadResults.where((r) => r.success).length}/${imageFiles.length} réussis';
        });
      } else {
        setState(() {
          _statusMessage = 'Aucune image sélectionnée';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Erreur: $e';
      });
      _showSnackBar('Erreur: $e');
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  Future<void> _uploadSingleFile(File file, {bool showIndividualStatus = true}) async {
    final fileName = file.path.split('/').last;
    
    if (showIndividualStatus) {
      setState(() {
        _statusMessage = 'Téléchargement de $fileName...';
      });
    }

    final String? fileId = await _driveService.uploadImage(
      filePath: file.path,
      folderId: widget.folderId,
      description: 'Téléchargé depuis VitaBrosse Pro - Rapport',
    );

    final result = fileId != null
        ? UploadResult.success(
            driveUrl: _driveService.getPublicUrl(fileId),
            fileId: fileId,
          )
        : UploadResult.failure(
            error: 'Échec du téléchargement de $fileName',
          );

    setState(() {
      _uploadResults.add(result);
    });

    if (showIndividualStatus) {
      if (fileId != null) {
        setState(() {
          _statusMessage = 'Image téléchargée avec succès!';
        });
        _showSnackBar('Image téléchargée! ID: $fileId');
      } else {
        setState(() {
          _statusMessage = 'Échec du téléchargement';
        });
        _showSnackBar('Échec du téléchargement');
      }
    }

    // Notify parent widget
    if (widget.onUploadComplete != null) {
      widget.onUploadComplete!(_uploadResults);
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  Future<void> _signOutAndReconnect() async {
    try {
      setState(() {
        _statusMessage = 'Déconnexion en cours...';
        _isInitialized = false;
      });

      await _driveService.signOut();
      
      setState(() {
        _statusMessage = 'Reconnexion avec un autre compte...';
      });

      await _initializeDriveService();
    } catch (e) {
      setState(() {
        _statusMessage = 'Erreur lors du changement de compte: $e';
      });
      _showSnackBar('Erreur: $e');
    }
  }

  void _showUploadedFiles() {
    if (_uploadResults.isEmpty) {
      _showSnackBar('Aucun fichier téléchargé');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fichiers téléchargés'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _uploadResults.length,
            itemBuilder: (context, index) {
              final result = _uploadResults[index];
              return ListTile(
                leading: Icon(
                  result.success ? Icons.check_circle : Icons.error,
                  color: result.success ? Colors.green : Colors.red,
                ),
                title: Text('Fichier ${index + 1}'),
                subtitle: result.success 
                    ? Text('ID: ${result.fileId}')
                    : Text('Erreur: ${result.error}'),
                trailing: result.success
                    ? IconButton(
                        icon: const Icon(Icons.open_in_new),
                        onPressed: () {
                          _showSnackBar('URL: ${result.driveUrl}');
                        },
                      )
                    : null,
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.cloud_upload,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  widget.title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Status message
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _isInitialized 
                    ? Colors.green.withOpacity(0.1)
                    : Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _isInitialized 
                      ? Colors.green.withOpacity(0.3)
                      : Colors.orange.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  if (_isUploading)
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  else
                    Icon(
                      _isInitialized ? Icons.check_circle : Icons.info,
                      color: _isInitialized ? Colors.green : Colors.orange,
                      size: 16,
                    ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _statusMessage,
                      style: TextStyle(
                        color: _isInitialized ? Colors.green[700] : Colors.orange[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Upload buttons
            if (_isInitialized) ...[
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isUploading ? null : _uploadImageFromGallery,
                      icon: const Icon(Icons.photo_library),
                      label: const Text('Galerie'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isUploading ? null : _uploadImageFromCamera,
                      icon: const Icon(Icons.camera_alt),
                      label: const Text('Appareil photo'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
              if (widget.allowMultiple) ...[
                const SizedBox(height: 8),
                ElevatedButton.icon(
                  onPressed: _isUploading ? null : _uploadMultipleImages,
                  icon: const Icon(Icons.photo_library_outlined),
                  label: const Text('Sélection multiple'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ],
              const SizedBox(height: 8),
              // Sign out button
              OutlinedButton.icon(
                onPressed: _isUploading ? null : _signOutAndReconnect,
                icon: const Icon(Icons.logout),
                label: const Text('Changer de compte'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
              ),
            ] else ...[
              // Retry button when not initialized
              ElevatedButton.icon(
                onPressed: _initializeDriveService,
                icon: const Icon(Icons.refresh),
                label: const Text('Réessayer la connexion'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ],

            // Show uploaded files button
            if (_uploadResults.isNotEmpty) ...[
              const SizedBox(height: 16),
              OutlinedButton.icon(
                onPressed: _showUploadedFiles,
                icon: const Icon(Icons.list),
                label: Text('Voir les fichiers (${_uploadResults.length})'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}