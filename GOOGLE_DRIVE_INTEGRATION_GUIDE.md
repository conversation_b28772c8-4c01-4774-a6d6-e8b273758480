# Guide d'intégration Google Drive - VitaBrosse Pro

## Vue d'ensemble

Cette intégration permet aux utilisateurs de télécharger automatiquement les photos des rapports de merchandising vers Google Drive, offrant un stockage cloud sécurisé et un accès facile aux images.

## Fonctionnalités

- ✅ Téléchargement d'images depuis la galerie
- ✅ Prise de photos avec l'appareil photo
- ✅ Sélection multiple d'images
- ✅ Téléchargement automatique vers Google Drive
- ✅ URLs publiques pour accès facile
- ✅ Gestion des erreurs et statut de téléchargement
- ✅ Interface utilisateur intuitive

## Architecture

### Composants principaux

1. **DriveService** (`lib/services/drive_service.dart`)
   - Gère l'authentification avec Google Drive API
   - Télécharge les fichiers vers Google Drive
   - Crée des dossiers et gère les permissions

2. **GoogleDriveUploadWidget** (`lib/widgets/google_drive_upload_widget.dart`)
   - Widget réutilisable pour l'interface de téléchargement
   - Gère la sélection d'images et l'affichage du statut
   - Callback pour notifier les téléchargements terminés

3. **FilePickerHelper** (`lib/utils/file_picker_helper.dart`)
   - Utilitaires pour la sélection de fichiers
   - Support galerie, appareil photo et sélection multiple

4. **UploadResult** (`lib/models/upload_result.dart`)
   - Modèle pour les résultats de téléchargement
   - Contient l'URL, l'ID du fichier et les erreurs

## Configuration

### 1. Dépendances

Les dépendances suivantes ont été ajoutées au `pubspec.yaml`:

```yaml
dependencies:
  googleapis: ^11.4.0
  googleapis_auth: ^1.4.1
  crypto: ^3.0.3
  mime: ^1.0.4
```

### 2. Compte de service Google

Le fichier `assets/credentials/service_account.json` contient les credentials du compte de service Google Cloud configuré pour accéder à l'API Google Drive.

### 3. Permissions

Le compte de service a les permissions suivantes:
- `https://www.googleapis.com/auth/drive.file` - Accès aux fichiers créés par l'application

## Utilisation

### Dans l'écran de création de rapport

```dart
GoogleDriveUploadWidget(
  title: 'Photos du rapport',
  allowMultiple: true,
  onUploadComplete: (results) {
    setState(() {
      _driveUploadResults = results;
    });
  },
),
```

### Récupération des URLs

Les URLs des fichiers téléchargés sont automatiquement ajoutées au rapport:

```dart
final List<String> allPhotos = [
  // URLs Google Drive des téléchargements réussis
  ..._driveUploadResults
      .where((result) => result.success && result.driveUrl != null)
      .map((result) => result.driveUrl!),
];
```

## Fonctionnement

### 1. Initialisation

Le service s'initialise automatiquement lors du premier usage:
- Authentification avec le compte de service
- Configuration de l'API Google Drive

### 2. Téléchargement

Pour chaque image sélectionnée:
- Validation du type de fichier (images uniquement)
- Téléchargement vers Google Drive
- Création d'un lien public
- Retour du résultat (succès/échec)

### 3. Stockage

Les URLs des images sont stockées dans le modèle `RapportMerchandising`:
- Format: URLs publiques Google Drive
- Accès direct sans authentification
- Intégration transparente avec l'interface existante

## Interface utilisateur

### Statuts d'affichage

- 🔄 **Initialisation** - Configuration du service
- ✅ **Prêt** - Service initialisé, prêt pour téléchargement
- 📤 **Téléchargement** - Upload en cours
- ✅ **Succès** - Téléchargement terminé
- ❌ **Erreur** - Problème lors du téléchargement

### Boutons d'action

- **Galerie** - Sélection depuis la galerie photos
- **Appareil photo** - Prise de photo directe
- **Sélection multiple** - Choix de plusieurs images
- **Voir les fichiers** - Liste des téléchargements

## Gestion des erreurs

### Erreurs communes

1. **Initialisation échouée**
   - Vérifier les credentials du compte de service
   - Vérifier la connectivité internet

2. **Téléchargement échoué**
   - Fichier trop volumineux
   - Format non supporté
   - Problème de réseau

3. **Permissions insuffisantes**
   - Vérifier les permissions du compte de service
   - Vérifier la configuration Google Cloud

### Messages d'erreur

Les erreurs sont affichées à l'utilisateur avec des messages explicites en français.

## Sécurité

- Authentification par compte de service (plus sécurisé que OAuth)
- Credentials stockés localement dans l'application
- Accès limité aux fichiers créés par l'application
- URLs publiques mais non indexables

## Performance

- Compression automatique des images (qualité 85%)
- Téléchargement asynchrone
- Interface non bloquante
- Gestion de la mémoire optimisée

## Maintenance

### Mise à jour des credentials

Pour mettre à jour les credentials du compte de service:
1. Remplacer le fichier `assets/credentials/service_account.json`
2. Mettre à jour les constantes dans `DriveService`
3. Recompiler l'application

### Monitoring

Les logs sont disponibles dans la console pour:
- Statut d'initialisation
- Progression des téléchargements
- Erreurs et exceptions

## Support

Pour toute question ou problème:
1. Vérifier les logs de l'application
2. Tester la connectivité Google Drive
3. Vérifier les permissions du compte de service