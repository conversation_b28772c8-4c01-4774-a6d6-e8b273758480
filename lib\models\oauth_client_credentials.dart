class OAuthClientCredentials {
  final String clientId;
  final String projectId;
  final String authUri;
  final String tokenUri;
  final String authProviderX509CertUrl;

  const OAuthClientCredentials({
    required this.clientId,
    required this.projectId,
    required this.authUri,
    required this.tokenUri,
    required this.authProviderX509CertUrl,
  });

  factory OAuthClientCredentials.fromJson(Map<String, dynamic> json) {
    final installed = json['installed'] as Map<String, dynamic>;
    return OAuthClientCredentials(
      clientId: installed['client_id'] as String,
      projectId: installed['project_id'] as String,
      authUri: installed['auth_uri'] as String,
      tokenUri: installed['token_uri'] as String,
      authProviderX509CertUrl: installed['auth_provider_x509_cert_url'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'installed': {
        'client_id': clientId,
        'project_id': projectId,
        'auth_uri': authUri,
        'token_uri': tokenUri,
        'auth_provider_x509_cert_url': authProviderX509CertUrl,
      }
    };
  }
}